'use client';

import { StyleSelectorProps, WaveformStyle } from '@/types';

interface StyleOption {
  id: WaveformStyle;
  name: string;
  description: string;
  preview: string;
}

const STYLE_OPTIONS: StyleOption[] = [
  {
    id: 'circular_spectrum',
    name: 'Circular Spectrum',
    description: 'Beautiful circular frequency analyzer with radial bars',
    preview: '⭕'
  }
];

export default function StyleSelector({ selectedStyle, onStyleChange, disabled = false }: StyleSelectorProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      {STYLE_OPTIONS.map((style) => (
        <button
          key={style.id}
          onClick={() => onStyleChange(style.id)}
          disabled={disabled}
          className={`
            relative p-4 rounded-lg border-2 transition-all duration-200 text-left
            ${selectedStyle === style.id 
              ? 'border-purple-400 bg-purple-400/20' 
              : 'border-gray-600 hover:border-gray-500 bg-gray-800/50'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-105'}
          `}
        >
          <div className="flex flex-col items-center space-y-2">
            <div className="text-2xl">{style.preview}</div>
            <div className="text-center">
              <h3 className="font-medium text-white text-sm">{style.name}</h3>
              <p className="text-xs text-gray-400 mt-1">{style.description}</p>
            </div>
          </div>
          
          {selectedStyle === style.id && (
            <div className="absolute top-2 right-2">
              <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
            </div>
          )}
        </button>
      ))}
    </div>
  );
}
