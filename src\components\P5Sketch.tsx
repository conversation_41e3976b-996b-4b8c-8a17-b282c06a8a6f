'use client';

import { useEffect, useRef } from 'react';
import { WaveformStyle, VisualizationParams } from '@/types';

interface P5SketchProps {
  audioData: Uint8Array;
  style: WaveformStyle;
  width: number;
  height: number;
  isPlaying: boolean;
  visualizationParams: VisualizationParams;
}

export default function P5Sketch({ audioData, style, width, height, isPlaying, visualizationParams }: P5SketchProps) {
  const sketchRef = useRef<HTMLDivElement>(null);
  const p5InstanceRef = useRef<any>(null);
  const isPlayingRef = useRef(isPlaying);
  const audioDataRef = useRef(audioData);
  const styleRef = useRef(style);
  const visualizationParamsRef = useRef(visualizationParams);

  // Update refs when props change (without recreating p5 instance)
  useEffect(() => {
    isPlayingRef.current = isPlaying;
  }, [isPlaying]);

  useEffect(() => {
    audioDataRef.current = audioData;
  }, [audioData]);

  useEffect(() => {
    styleRef.current = style;
  }, [style]);

  useEffect(() => {
    visualizationParamsRef.current = visualizationParams;
  }, [visualizationParams]);

  // Create p5 instance only once
  useEffect(() => {
    if (!sketchRef.current || typeof window === 'undefined') return;

    // Only create if we don't have an instance
    if (p5InstanceRef.current) return;

    // Dynamically import p5.js only on client side
    const loadP5 = async () => {
      const p5 = (await import('p5')).default;

      const sketch = (p: any) => {
      let time = 0;

      p.setup = () => {
        p.createCanvas(width, height);
        p.colorMode(p.HSB, 360, 100, 100, 100);
      };

      p.draw = () => {
        // CLEAR BACKGROUND COMPLETELY to prevent traces
        p.background(0, 0, 0); // Solid black background

        // Calculate energy-based time progression
        const currentData = audioDataRef.current;
        let energyFactor = 1;

        if (currentData && currentData.length > 0) {
          const totalEnergy = Array.from(currentData).reduce((sum, val) => sum + val, 0) / currentData.length;
          energyFactor = 1 + (totalEnergy / 255) * 2; // Speed up animation based on energy
        }

        // Always update time for smooth animation, but slower when paused
        if (isPlayingRef.current) {
          time += 0.025 * energyFactor; // Dynamic speed based on energy
        } else {
          time += 0.008; // Slower animation when paused
        }

        // No special background effects needed for circular spectrum

        // Draw waveform based on style using refs
        drawWaveform(p, audioDataRef.current, styleRef.current, time);
      };

      const drawWaveform = (p: any, data: Uint8Array, style: WaveformStyle, time: number) => {
        const centerX = width / 2;
        const centerY = height / 2;

        // Only circular spectrum style is available
        drawCircularSpectrum(p, data, centerX, centerY, time);
      };













      const drawCircularSpectrum = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
        // Check if data has any meaningful values
        const maxValue = Math.max(...Array.from(data));
        const hasData = maxValue > 0;
        const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;

        // Circle parameters - scale based on resolution
        const minDimension = Math.min(width, height);
        const scaleFactor = minDimension / 360; // Base scale on 360p resolution
        const baseRadius = Math.floor(80 * scaleFactor);
        const maxBarLength = Math.floor(120 * scaleFactor);
        const numBars = hasData ? Math.min(data.length, 128) : 64; // Limit for performance

        // Draw frequency bars in a circle
        for (let i = 0; i < numBars; i++) {
          // Calculate angle with rotation
          const angle = (i / numBars) * p.TWO_PI + time * 0.2;
          let amplitude = hasData ? data[Math.floor(i * data.length / numBars)] / 255 :
                         p.sin(i * 0.1 + time * 2) * 0.5 + 0.5;

          // Smooth amplitude changes to prevent flickering
          amplitude = p.constrain(amplitude, 0, 1);

          const barLength = amplitude * maxBarLength * visualizationParamsRef.current.sensitivity;
          const barWidth = Math.max(2, (p.TWO_PI / numBars) * baseRadius * 0.6); // Bar thickness

          // Calculate bar position relative to center
          const startX = centerX + p.cos(angle) * baseRadius;
          const startY = centerY + p.sin(angle) * baseRadius;
          const endX = centerX + p.cos(angle) * (baseRadius + barLength);
          const endY = centerY + p.sin(angle) * (baseRadius + barLength);

          // Color based on frequency position and amplitude
          const hue = (i * 360 / numBars + time * 30) % 360;
          const saturation = 70 + amplitude * 30;
          const brightness = 60 + amplitude * 40;
          const alpha = 80 + amplitude * 20;

          // Draw main bar
          p.stroke(hue, saturation, brightness, alpha);
          p.strokeWeight(barWidth);
          p.line(startX, startY, endX, endY);

          // Add glow effect for higher amplitudes
          if (amplitude > 0.3) {
            p.stroke(hue, saturation * 0.6, brightness, alpha * 0.4);
            p.strokeWeight(barWidth * 1.5);
            p.line(startX, startY, endX, endY);
          }

          // Add inner glow
          if (amplitude > 0.6) {
            p.stroke(hue, 40, 90, alpha * 0.6);
            p.strokeWeight(barWidth * 2);
            p.line(startX, startY, endX, endY);
          }

          // Add particle effects at bar tips for high energy - scale particle size
          if (amplitude > 0.7) {
            p.fill(hue, saturation, brightness, amplitude * 100);
            p.noStroke();
            const particleSize = amplitude * 8 * scaleFactor;
            p.circle(endX, endY, particleSize);

            // Add sparkle effect
            p.fill(0, 0, 100, amplitude * 80);
            p.circle(endX, endY, particleSize * 0.5);
          }
        }

        // Draw center circle - scale based on resolution
        p.noStroke();
        const baseCenterSize = Math.floor((20 + (totalEnergy / 255) * 30) * scaleFactor);
        const centerSize = Math.max(10, baseCenterSize); // Ensure minimum size

        // Center circle with gradient effect
        const stepSize = Math.max(1, Math.floor(2 * scaleFactor));
        for (let r = centerSize; r > 0; r -= stepSize) {
          const alpha = p.map(r, 0, centerSize, 100, 0);
          const hue = (time * 50) % 360;
          p.fill(hue, 60, 90, alpha);
          p.circle(centerX, centerY, r);
        }

        // Add pulsing ring around center - scale pulse amplitude
        const pulseAmplitude = Math.floor(10 * scaleFactor);
        const pulseRadius = centerSize + p.sin(time * 4) * pulseAmplitude;
        p.noFill();
        p.stroke(280, 80, 90, 60);
        p.strokeWeight(Math.max(1, Math.floor(2 * scaleFactor)));
        p.circle(centerX, centerY, pulseRadius);
      };


    };

      // Create p5 instance
      if (sketchRef.current) {
        p5InstanceRef.current = new p5(sketch, sketchRef.current);
      }
    };

    loadP5();

    return () => {
      if (p5InstanceRef.current) {
        try {
          p5InstanceRef.current.remove();
        } catch (error) {
          console.warn('Error removing p5 instance:', error);
        }
        p5InstanceRef.current = null;
      }
    };
  }, [width, height]); // Only recreate when canvas size changes

  // Additional cleanup on component unmount
  useEffect(() => {
    return () => {
      if (p5InstanceRef.current) {
        try {
          p5InstanceRef.current.remove();
        } catch (error) {
          console.warn('Error removing p5 instance on unmount:', error);
        }
        p5InstanceRef.current = null;
      }
    };
  }, []);

  return <div ref={sketchRef} className="w-full h-full" />;
}
