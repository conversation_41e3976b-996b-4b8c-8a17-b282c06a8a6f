'use client';

import { useState, useRef, useEffect } from 'react';
import { Download, Play, AlertCircle, CheckCircle } from 'lucide-react';
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { toBlobURL } from '@ffmpeg/util';
import type p5 from 'p5'; // MODIFICA: Importa solo i TIPI di p5 per TypeScript, non la libreria

import { VideoGeneratorProps, ProcessingStatus, WaveformStyle } from '@/types';

export default function VideoGenerator({
  audioData,
  style,
  settings,
  visualizationParams,
  onGenerationStart,
  onGenerationComplete
}: VideoGeneratorProps) {
  const [status, setStatus] = useState<ProcessingStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('');
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const ffmpegRef = useRef<FFmpeg | null>(null);
  const [ffmpegLoaded, setFFmpegLoaded] = useState(false);

  // Get quality settings based on selected quality level
  const getQualitySettings = (quality: 'medium' | 'high' | 'maximum') => {
    switch (quality) {
      case 'medium':
        return {
          videoBitrate: '1000k',
          preset: 'medium',
          crf: '28'
        };
      case 'high':
        return {
          videoBitrate: '2000k',
          preset: 'slow',
          crf: '23'
        };
      case 'maximum':
        return {
          videoBitrate: '4000k',
          preset: 'slower',
          crf: '18'
        };
      default:
        return {
          videoBitrate: '1000k',
          preset: 'medium',
          crf: '28'
        };
    }
  };

  // Cleanup URLs on unmount
  useEffect(() => {
    return () => {
      if (videoUrl) {
        URL.revokeObjectURL(videoUrl);
      }
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [videoUrl, previewUrl]);

  const loadFFmpeg = async () => {
    if (ffmpegRef.current || ffmpegLoaded) return;

    try {
      setMessage('Loading FFmpeg...');
      const ffmpeg = new FFmpeg();

      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';

      ffmpeg.on('log', ({ message }) => {
        console.log('FFmpeg log:', message);
      });

      ffmpeg.on('progress', ({ progress }) => {
        if (status === 'encoding_video') {
          setProgress(80 + (progress * 20)); // 80-100% for encoding
        }
      });

      await ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });

      ffmpegRef.current = ffmpeg;
      setFFmpegLoaded(true);
      console.log('FFmpeg loaded successfully');
    } catch (error) {
      console.error('Failed to load FFmpeg:', error);
      throw new Error('Failed to load FFmpeg. Please refresh and try again.');
    }
  };
  
  const createP5Instance = async (width: number, height: number): Promise<p5> => {
    // MODIFICA: Carica la libreria p5 dinamicamente solo quando questa funzione viene chiamata.
    const p5Module = await import('p5');
    const P5 = p5Module.default;

    return new Promise((resolve) => {
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        document.body.appendChild(tempContainer);

        const sketch = (p: p5) => {
            p.setup = () => {
                p.createCanvas(width, height);
                p.colorMode(p.HSB, 360, 100, 100, 100);
                p.noLoop();
                
                const originalRemove = p.remove;
                p.remove = () => {
                    originalRemove.call(p);
                    if (tempContainer.parentNode) {
                        document.body.removeChild(tempContainer);
                    }
                };
                resolve(p);
            };
        };

        new P5(sketch, tempContainer);
    });
  };


  const drawP5Waveform = (p: any, data: Uint8Array, style: WaveformStyle, time: number, width: number, height: number) => {
    const centerX = width / 2;
    const centerY = height / 2;
    p.background(0);

    // Only circular spectrum style is available
    drawCircularSpectrum(p, data, centerX, centerY, time, width, height);
  };















  const drawCircularSpectrum = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number, width: number, height: number) => {
    // Check if data has any meaningful values
    const maxValue = Math.max(...Array.from(data));
    const hasData = maxValue > 0;
    const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;

    // Circle parameters - scale based on resolution
    const minDimension = Math.min(width, height);
    const scaleFactor = minDimension / 360; // Base scale on 360p resolution
    const baseRadius = Math.floor(80 * scaleFactor);
    const maxBarLength = Math.floor(120 * scaleFactor);
    const numBars = hasData ? Math.min(data.length, 128) : 64; // Limit for performance

    // Draw frequency bars in a circle
    for (let i = 0; i < numBars; i++) {
      // Calculate angle with rotation
      const angle = (i / numBars) * p.TWO_PI + time * 0.2;
      let amplitude = hasData ? data[Math.floor(i * data.length / numBars)] / 255 :
                     p.sin(i * 0.1 + time * 2) * 0.5 + 0.5;

      // Smooth amplitude changes to prevent flickering
      amplitude = p.constrain(amplitude, 0, 1);

      const barLength = amplitude * maxBarLength * visualizationParams.sensitivity;
      const barWidth = Math.max(2, (p.TWO_PI / numBars) * baseRadius * 0.6); // Bar thickness

      // Calculate bar position relative to center
      const startX = centerX + p.cos(angle) * baseRadius;
      const startY = centerY + p.sin(angle) * baseRadius;
      const endX = centerX + p.cos(angle) * (baseRadius + barLength);
      const endY = centerY + p.sin(angle) * (baseRadius + barLength);

      // Color based on frequency position and amplitude
      const hue = (i * 360 / numBars + time * 30) % 360;
      const saturation = 70 + amplitude * 30;
      const brightness = 60 + amplitude * 40;
      const alpha = 80 + amplitude * 20;

      // Draw main bar
      p.stroke(hue, saturation, brightness, alpha);
      p.strokeWeight(barWidth);
      p.line(startX, startY, endX, endY);

      // Add glow effect for higher amplitudes
      if (amplitude > 0.3) {
        p.stroke(hue, saturation * 0.6, brightness, alpha * 0.4);
        p.strokeWeight(barWidth * 1.5);
        p.line(startX, startY, endX, endY);
      }

      // Add inner glow
      if (amplitude > 0.6) {
        p.stroke(hue, 40, 90, alpha * 0.6);
        p.strokeWeight(barWidth * 2);
        p.line(startX, startY, endX, endY);
      }

      // Add particle effects at bar tips for high energy - scale particle size
      if (amplitude > 0.7) {
        p.fill(hue, saturation, brightness, amplitude * 100);
        p.noStroke();
        const particleSize = amplitude * 8 * scaleFactor;
        p.circle(endX, endY, particleSize);

        // Add sparkle effect
        p.fill(0, 0, 100, amplitude * 80);
        p.circle(endX, endY, particleSize * 0.5);
      }
    }

    // Draw center circle - scale based on resolution
    p.noStroke();
    const baseCenterSize = Math.floor((20 + (totalEnergy / 255) * 30) * scaleFactor);
    const centerSize = Math.max(10, baseCenterSize); // Ensure minimum size

    // Center circle with gradient effect
    const stepSize = Math.max(1, Math.floor(2 * scaleFactor));
    for (let r = centerSize; r > 0; r -= stepSize) {
      const alpha = p.map(r, 0, centerSize, 100, 0);
      const hue = (time * 50) % 360;
      p.fill(hue, 60, 90, alpha);
      p.circle(centerX, centerY, r);
    }

    // Add pulsing ring around center - scale pulse amplitude
    const pulseAmplitude = Math.floor(10 * scaleFactor);
    const pulseRadius = centerSize + p.sin(time * 4) * pulseAmplitude;
    p.noFill();
    p.stroke(280, 80, 90, 60);
    p.strokeWeight(Math.max(1, Math.floor(2 * scaleFactor)));
    p.circle(centerX, centerY, pulseRadius);
  };


  const generatePreview = async () => {
    // ... (il resto della funzione è identico a prima, usando il p5Instance corretto)
    if (isGeneratingPreview || isGenerating) return;

    setIsGeneratingPreview(true);
    setStatus('generating_frames');
    setProgress(0);
    setMessage('Generating 3-second preview...');
    
    let p5Instance: p5 | null = null;

    try {
      if (!ffmpegLoaded) {
        setMessage('Loading FFmpeg...');
        await loadFFmpeg();
      }
      
      const [width, height] = settings.resolution.split('x').map(Number);
      
      p5Instance = await createP5Instance(width, height);

      setMessage('Generating preview frames...');
      
      const previewDuration = Math.min(3, audioData.metadata.duration);
      const totalFrames = Math.floor(previewDuration * settings.fps);
      console.log(`Generating ${totalFrames} frames for ${previewDuration}s preview at ${settings.fps}fps`);
      
      const frames: string[] = [];

      for (let frame = 0; frame < totalFrames; frame++) {
        const timeProgress = frame / totalFrames;
        const currentTime = timeProgress * previewDuration;

        const frameIndex = Math.floor((currentTime / audioData.metadata.duration) * audioData.frequencyData.length);
        const frequencyFrame = audioData.frequencyData[frameIndex] || new Float32Array(128);

        const audioDataArray = new Uint8Array(frequencyFrame.length);
        for (let i = 0; i < frequencyFrame.length; i++) {
          audioDataArray[i] = Math.min(255, Math.max(10, frequencyFrame[i] * 100));
        }
        
        const animationTime = currentTime * 2.5;

        drawP5Waveform(p5Instance, audioDataArray, style, animationTime, width, height);
        const frameDataUrl = (p5Instance.drawingContext.canvas as HTMLCanvasElement).toDataURL('image/png');
        frames.push(frameDataUrl);

        const frameProgress = (frame / totalFrames) * 60;
        setProgress(frameProgress);
        setMessage(`Generating preview frame ${frame + 1} of ${totalFrames}...`);

        if (frame % 5 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      setMessage('Preparing preview frames...');
      setProgress(65);
      // ... (resto della funzione invariato)
       const frameFiles: { name: string; data: Uint8Array }[] = [];

      for (let i = 0; i < frames.length; i++) {
        const response = await fetch(frames[i]);
        const blob = await response.blob();
        const arrayBuffer = await blob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        const frameName = `preview_frame_${i.toString().padStart(6, '0')}.png`;
        frameFiles.push({ name: frameName, data: uint8Array });

        if (i % 10 === 0) {
          setProgress(65 + (i / frames.length) * 10);
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      setMessage('Preparing preview frames...');
      setProgress(80);
      setStatus('encoding_video');
      setMessage('Encoding preview video...');
      setProgress(85);
      const ffmpeg = ffmpegRef.current!;
      for (let i = 0; i < frameFiles.length; i++) {
        await ffmpeg.writeFile(frameFiles[i].name, frameFiles[i].data);
        if (i % 20 === 0) {
          setProgress(85 + (i / frameFiles.length) * 10);
        }
      }
      setMessage('Finalizing preview...');
      setProgress(95);
      const previewOutputName = 'preview_output.mp4';
      const qualitySettings = getQualitySettings(settings.quality);
      await ffmpeg.exec([
        '-framerate', settings.fps.toString(),
        '-i', 'preview_frame_%06d.png',
        '-c:v', 'libx264',
        '-preset', qualitySettings.preset,
        '-crf', qualitySettings.crf,
        '-b:v', qualitySettings.videoBitrate,
        '-pix_fmt', 'yuv420p',
        '-an', // No audio
        previewOutputName
      ]);
      const previewData = await ffmpeg.readFile(previewOutputName);
      const previewBlob = new Blob([previewData], { type: 'video/mp4' });
      const previewVideoUrl = URL.createObjectURL(previewBlob);
      setPreviewUrl(previewVideoUrl);
      setProgress(100);
      setStatus('complete');
      setMessage('Preview generated successfully!');
    } catch (error) {
      console.error('Preview generation error:', error);
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Failed to generate preview');
    } finally {
      if (p5Instance) {
        p5Instance.remove();
      }
      setIsGeneratingPreview(false);
    }
  };

  const generateVideo = async () => {
    // ... (il resto della funzione è identico a prima, usando il p5Instance corretto)
    if (isGenerating || isGeneratingPreview) return;

    if (audioData.metadata.duration > 900) {
      setStatus('error');
      setMessage('Audio too long. Maximum duration is 15 minutes for video generation.');
      return;
    }

    setIsGenerating(true);
    onGenerationStart();
    setStatus('generating_frames');
    setProgress(0);
    setMessage('Preparing full video generation...');

    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl('');
    }

    let p5Instance: p5 | null = null;

    try {
      setMessage('Analyzing audio frequencies...');
      await new Promise(resolve => setTimeout(resolve, 100));
      setProgress(10);
      
      const [width, height] = settings.resolution.split('x').map(Number);
      
      p5Instance = await createP5Instance(width, height);

      const totalFrames = Math.floor(audioData.metadata.duration * settings.fps);
      console.log(`Generating ${totalFrames} frames for ${audioData.metadata.duration}s video at ${settings.fps}fps`);
      
      const frames: string[] = [];
      for (let frame = 0; frame < totalFrames; frame++) {
        const timeProgress = frame / totalFrames;
        const currentTime = timeProgress * audioData.metadata.duration;

        const frameIndex = Math.floor(timeProgress * audioData.frequencyData.length);
        const frequencyFrame = audioData.frequencyData[frameIndex] || new Float32Array(128);

        const audioDataArray = new Uint8Array(frequencyFrame.length);
        for (let i = 0; i < frequencyFrame.length; i++) {
          audioDataArray[i] = Math.min(255, Math.max(10, frequencyFrame[i] * 100));
        }

        const animationTime = currentTime * 2.5;

        drawP5Waveform(p5Instance, audioDataArray, style, animationTime, width, height);
        const frameDataUrl = (p5Instance.drawingContext.canvas as HTMLCanvasElement).toDataURL('image/png');
        frames.push(frameDataUrl);

        const frameProgress = (frame / totalFrames) * 70;
        setProgress(10 + frameProgress);
        setMessage(`Generating frame ${frame + 1} of ${totalFrames}...`);

        if (frame % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      if (!ffmpegLoaded) {
        setMessage('Loading FFmpeg...');
        await loadFFmpeg();
      }
      setMessage('Preparing frames for encoding...');
      setProgress(82);
      // ... (resto della funzione invariato)
      const frameFiles: { name: string; data: Uint8Array }[] = [];
      for (let i = 0; i < frames.length; i++) {
        const response = await fetch(frames[i]);
        const blob = await response.blob();
        const arrayBuffer = await blob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);
        const frameName = `frame_${i.toString().padStart(6, '0')}.png`;
        frameFiles.push({ name: frameName, data: uint8Array });
        if (i % 50 === 0) {
          setProgress(82 + (i / frames.length) * 3);
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }
      setMessage('Preparing video frames...');
      setProgress(85);
      setStatus('encoding_video');
      setMessage('Encoding video with FFmpeg...');
      setProgress(88);
      const ffmpeg = ffmpegRef.current!;
      setMessage('Writing frames to FFmpeg...');
      for (let i = 0; i < frameFiles.length; i++) {
        await ffmpeg.writeFile(frameFiles[i].name, frameFiles[i].data);
        if (i % 100 === 0) {
          setProgress(88 + (i / frameFiles.length) * 5);
        }
      }
      setMessage('Encoding final video...');
      setProgress(93);
      const outputName = 'output.mp4';
      const qualitySettings = getQualitySettings(settings.quality);
      await ffmpeg.exec([
        '-framerate', settings.fps.toString(),
        '-i', 'frame_%06d.png',
        '-c:v', 'libx264',
        '-preset', qualitySettings.preset,
        '-crf', qualitySettings.crf,
        '-b:v', qualitySettings.videoBitrate,
        '-pix_fmt', 'yuv420p',
        '-an', // No audio
        outputName
      ]);
      const data = await ffmpeg.readFile(outputName);
      const videoBlob = new Blob([data], { type: 'video/mp4' });
      const url = URL.createObjectURL(videoBlob);
      setVideoUrl(url);
      setProgress(100);
      setStatus('complete');
      setMessage('Video generated successfully!');

    } catch (error) {
      console.error('Video generation error:', error);
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Failed to generate video');
    } finally {
      if (p5Instance) {
        p5Instance.remove();
      }
      setIsGenerating(false);
      onGenerationComplete();
    }
  };

  const downloadVideo = () => {
    if (videoUrl) {
      const a = document.createElement('a');
      a.href = videoUrl;
      a.download = `${audioData.metadata.fileName.replace(/\.[^/.]+$/, '')}-visualization.mp4`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'generating_frames':
      case 'encoding_video':
        return <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />;
      case 'complete':
        return <CheckCircle className="w-4 h-4" />;
      case 'error':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Play className="w-4 h-4" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'complete':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-white';
    }
  };

  return (
    // ... (JSX invariato)
    <div className="space-y-4">
      {/* Preview and Generation buttons */}
      <div className="space-y-3">
        {/* Preview button */}
        <button
          onClick={generatePreview}
          disabled={isGenerating || isGeneratingPreview}
          className={`
            w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2
            ${isGenerating || isGeneratingPreview
              ? 'bg-gray-600 cursor-not-allowed text-gray-400'
              : 'bg-blue-600 hover:bg-blue-700 active:scale-95 text-white'
            }
          `}
        >
          <Play className="w-5 h-5" />
          <span>
            {isGeneratingPreview ? 'Generating Preview...' : 'Generate 3s Preview'}
          </span>
        </button>

        {/* Full video generation button */}
        <button
          onClick={generateVideo}
          disabled={isGenerating || isGeneratingPreview}
          className={`
            w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2
            ${isGenerating || isGeneratingPreview
              ? 'bg-gray-600 cursor-not-allowed'
              : 'bg-purple-600 hover:bg-purple-700 active:scale-95'
            }
          `}
        >
          {getStatusIcon()}
          <span className={getStatusColor()}>
            {isGenerating ? 'Generating Full Video...' : 'Generate Full Video'}
          </span>
        </button>
      </div>

      {/* Preview video */}
      {previewUrl && !isGenerating && (
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-white">Preview (3 seconds)</h3>
          <video
            src={previewUrl}
            controls
            className="w-full rounded-lg bg-black"
            style={{ maxHeight: '300px' }}
          >
            Your browser does not support the video tag.
          </video>
          <button
            onClick={() => {
              const a = document.createElement('a');
              a.href = previewUrl;
              a.download = `preview-${audioData.metadata.fileName.replace(/\.[^/.]+$/, '')}.mp4`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            }}
            className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Download Preview</span>
          </button>
        </div>
      )}

      {/* Progress indicator */}
      {(isGenerating || isGeneratingPreview) && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-300">{message}</span>
            <span className="text-gray-300">{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-purple-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Status message */}
      {status !== 'idle' && !isGenerating && !isGeneratingPreview && (
        <div className={`text-center py-2 ${getStatusColor()}`}>
          {message}
        </div>
      )}

      {/* Download button */}
      {status === 'complete' && videoUrl && (
        <button
          onClick={downloadVideo}
          className="w-full py-3 px-6 bg-green-600 hover:bg-green-700 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2"
        >
          <Download className="w-5 h-5" />
          <span>Download Video</span>
        </button>
      )}

      {/* Video info */}
      {status === 'complete' && videoUrl && !isGenerating && (
        <div className="text-sm text-gray-400 space-y-1">
          <p>Resolution: {settings.resolution}</p>
          <p>Frame Rate: {settings.fps} FPS</p>
          <p>Duration: {Math.round(audioData.metadata.duration)}s</p>
          <p>Style: {style.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
        </div>
      )}
    </div>
  );
}